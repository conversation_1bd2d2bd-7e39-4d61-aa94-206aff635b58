{"time":"2025-07-05T14:09:45.221Z","level":"INFO","message":"Recommender service initialized successfully"}
{"time":"2025-07-05T14:09:45.221Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-07-05T15:03:34.723Z","level":"INFO","message":"Recommender service initialized successfully"}
{"time":"2025-07-05T15:03:34.722Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-07-05T15:03:34.727Z","level":"ERROR","message":"Failed to initialize audio analyzer service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-07-05T15:03:34.803Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:34.808Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:34.811Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:34.840Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:34.842Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:34.844Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:34.870Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:34.871Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:34.873Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:34.908Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:34.909Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:34.910Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:34.947Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:34.947Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:34.949Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:34.957Z","level":"INFO","message":"Task started","taskId":1,"taskType":"media_transcribe","description":"Test transcribe task"}
{"time":"2025-07-05T15:03:34.959Z","level":"INFO","message":"Starting media transcription task","taskId":1,"filePath":"/nonexistent/video.mp4","whisperModel":"turbo"}
{"time":"2025-07-05T15:03:34.960Z","level":"ERROR","message":"Media transcription task failed","taskId":1,"error":"ENOENT: no such file or directory, stat '/nonexistent/video.mp4'"}
{"time":"2025-07-05T15:03:34.965Z","level":"ERROR","message":"Task failed","taskId":1,"taskType":"media_transcribe","errorReason":"ENOENT: no such file or directory, stat '/nonexistent/video.mp4'","durationMs":10}
{"time":"2025-07-05T15:03:34.992Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:34.993Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:34.995Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:35.001Z","level":"INFO","message":"Task started","taskId":2,"taskType":"media_tag","description":"Test tag task"}
{"time":"2025-07-05T15:03:35.003Z","level":"INFO","message":"Starting media tagging task","taskId":2,"filePath":"/nonexistent/video.mp4"}
{"time":"2025-07-05T15:03:35.004Z","level":"ERROR","message":"Media metadata not found. Run media_ingest first.","taskId":2,"filePath":"/nonexistent/video.mp4"}
{"time":"2025-07-05T15:03:35.009Z","level":"ERROR","message":"Task failed","taskId":2,"taskType":"media_tag","errorReason":"Media metadata not found. Run media_ingest first.","durationMs":9}
{"time":"2025-07-05T15:03:35.038Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:35.038Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:35.044Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:35.050Z","level":"INFO","message":"Task started","taskId":3,"taskType":"index_meili","description":"Test Meilisearch index task"}
{"time":"2025-07-05T15:03:35.051Z","level":"INFO","message":"Starting Meilisearch indexing task","taskId":3,"mediaId":999}
{"time":"2025-07-05T15:03:35.052Z","level":"ERROR","message":"Media metadata not found","taskId":3,"mediaId":999}
{"time":"2025-07-05T15:03:35.057Z","level":"ERROR","message":"Task failed","taskId":3,"taskType":"index_meili","errorReason":"Media metadata not found","durationMs":8}
{"time":"2025-07-05T15:03:35.083Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:35.084Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:35.086Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:35.091Z","level":"INFO","message":"Task started","taskId":4,"taskType":"index_chroma","description":"Test ChromaDB index task"}
{"time":"2025-07-05T15:03:35.093Z","level":"INFO","message":"Starting ChromaDB indexing task","taskId":4,"mediaId":999}
{"time":"2025-07-05T15:03:35.094Z","level":"ERROR","message":"Media metadata not found","taskId":4,"mediaId":999}
{"time":"2025-07-05T15:03:35.099Z","level":"ERROR","message":"Task failed","taskId":4,"taskType":"index_chroma","errorReason":"Media metadata not found","durationMs":7}
{"time":"2025-07-05T15:03:35.133Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:35.134Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:35.136Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:35.169Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:35.170Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:35.174Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-07-05T15:03:35.208Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-07-05T15:03:35.209Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-07-05T15:03:35.211Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
