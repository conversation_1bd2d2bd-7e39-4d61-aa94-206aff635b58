import { describe, it, expect, beforeEach, afterEach, mock, afterAll } from 'bun:test';
import { promises as fs } from 'fs';

// Mock config
const mockConfig = {
    paths: {
        outputs: '/tmp/test-outputs',
        logs: '/tmp/test-logs'
    },
    ollama: {
        url: 'http://localhost:11434',
        model: 'test-model'
    }
};

// Mock logger
const mockLogger = {
    info: mock(() => Promise.resolve()),
    error: mock(() => Promise.resolve()),
    warn: mock(() => Promise.resolve()),
    debug: mock(() => Promise.resolve())
};

// Mock spawn function
const mockSpawn = mock(() => ({
    stdout: new ReadableStream({
        start(controller) {
            controller.enqueue(new TextEncoder().encode('Mock command output'));
            controller.close();
        }
    }),
    stderr: new ReadableStream({
        start(controller) {
            controller.close();
        }
    }),
    exited: Promise.resolve(0)
}));

describe('ToolRunner', () => {
    let ToolRunner: any;
    let toolRunner: any;

    beforeEach(async () => {
        // Apply mocks before importing
        mock.module('../src/config', () => ({ config: mockConfig }));
        mock.module('../src/utils/logger', () => ({ logger: mockLogger }));
        mock.module('bun', () => ({
            spawn: mockSpawn,
            file: (path: string) => ({
                exists: () => Promise.resolve(true),
                size: 1024
            })
        }));

        // Create test directories
        await fs.mkdir(mockConfig.paths.outputs, { recursive: true });
        await fs.mkdir(mockConfig.paths.logs, { recursive: true });

        // Import modules after mocking with cache busting
        const module = await import('../src/tools/tool_runner?t=' + Date.now());
        ToolRunner = module.ToolRunner;
        toolRunner = module.toolRunner;
    });

    afterEach(async () => {
        // Clean up test directories
        try {
            await fs.rm('/tmp/test-outputs', { recursive: true, force: true });
            await fs.rm('/tmp/test-logs', { recursive: true, force: true });
        } catch (error) {
            // Ignore cleanup errors
        }

        // Clear all mocks
        mockSpawn.mockClear();
        mockLogger.info.mockClear();
        mockLogger.error.mockClear();
        mockLogger.warn.mockClear();
        mockLogger.debug.mockClear();
    });

    describe('Tool Execution', () => {
        it('should execute read_file tool', async () => {
            const testFile = `${mockConfig.paths.outputs}/test.txt`;
            await fs.writeFile(testFile, 'Test content');

            const result = await toolRunner.executeTool('read_file', {
                path: testFile
            });

            expect(result.content).toBe('Test content');
            expect(mockLogger.info).toHaveBeenCalledWith(
                'Executing tool',
                expect.objectContaining({ tool: 'read_file' })
            );
        });

        it('should handle unknown tools', async () => {
            await expect(
                toolRunner.executeTool('unknown_tool', {})
            ).rejects.toThrow('Unknown tool: unknown_tool');
        });

        it('should log tool execution', async () => {
            const testFile = `${mockConfig.paths.outputs}/log_test.txt`;
            await fs.writeFile(testFile, 'Log test content');

            await toolRunner.executeTool('read_file', { path: testFile });

            expect(mockLogger.info).toHaveBeenCalledWith(
                'Executing tool',
                expect.objectContaining({ tool: 'read_file' })
            );
            expect(mockLogger.info).toHaveBeenCalledWith(
                'Tool execution completed',
                expect.objectContaining({ tool: 'read_file' })
            );
        });

        it('should handle tool execution errors', async () => {
            await expect(
                toolRunner.executeTool('read_file', { path: '/nonexistent/file.txt' })
            ).rejects.toThrow();

            expect(mockLogger.error).toHaveBeenCalledWith(
                'Tool execution failed',
                expect.objectContaining({ tool: 'read_file' })
            );
        });
    });

    describe('Individual Tools', () => {
        describe('run_script tool', () => {
            it('should execute scripts', async () => {
                const scriptDir = `${mockConfig.paths.outputs}/scripts`;
                await fs.mkdir(scriptDir, { recursive: true });
                const scriptPath = `${scriptDir}/test.sh`;
                await fs.writeFile(scriptPath, '#!/bin/bash\necho "Script output"');
                await fs.chmod(scriptPath, 0o755);

                const result = await toolRunner.executeTool('run_script', {
                    script_name: 'test.sh',
                    args: []
                });

                expect(result.output).toContain('Script output');
                // Note: spawn mock doesn't work with the actual implementation,
                // but the script execution itself proves the tool works
            });
        });

        describe('read_file tool', () => {
            it('should read files', async () => {
                const testFile = `${mockConfig.paths.outputs}/test.txt`;
                await fs.writeFile(testFile, 'Test content');

                const result = await toolRunner.executeTool('read_file', { path: testFile });

                expect(result.content).toBe('Test content');
            });

            it('should handle missing files', async () => {
                await expect(
                    toolRunner.executeTool('read_file', { path: '/nonexistent/file.txt' })
                ).rejects.toThrow();
            });
        });

        describe('write_file tool', () => {
            it('should write files', async () => {
                const testFile = `${mockConfig.paths.outputs}/write_test.txt`;
                const content = 'Test write content';

                const result = await toolRunner.executeTool('write_file', {
                    path: testFile,
                    content: content
                });

                expect(result.path).toBe(testFile);

                // Verify file was written
                const written = await fs.readFile(testFile, 'utf-8');
                expect(written).toBe(content);
            });

            it('should create directories if needed', async () => {
                const testFile = `${mockConfig.paths.outputs}/subdir/test.txt`;
                await fs.mkdir(`${mockConfig.paths.outputs}/subdir`, { recursive: true });

                const result = await toolRunner.executeTool('write_file', {
                    path: testFile,
                    content: 'test'
                });

                expect(result.path).toBe(testFile);

                // Verify directory exists
                const stats = await fs.stat(`${mockConfig.paths.outputs}/subdir`);
                expect(stats.isDirectory()).toBe(true);
            });
        });

        describe('ollama_chat tool', () => {
            it('should make chat requests', async () => {
                // Mock fetch for ollama
                const mockFetch = mock(() => Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({ response: 'Mocked response' })
                }));
                global.fetch = mockFetch;

                const result = await toolRunner.executeTool('ollama_chat', {
                    prompt: 'Test prompt',
                    model: 'test-model'
                });

                expect(result.response).toBe('Mocked response');
                expect(mockFetch).toHaveBeenCalled();
            });
        });
    });

    describe('Error Handling', () => {
        it('should handle file system errors', async () => {
            await expect(
                toolRunner.executeTool('read_file', { path: '/root/protected_file.txt' })
            ).rejects.toThrow();
        });

        it('should handle invalid tool arguments', async () => {
            await expect(
                toolRunner.executeTool('write_file', {}) // Missing required path and content
            ).rejects.toThrow();
        });

        it('should handle missing required arguments', async () => {
            await expect(
                toolRunner.executeTool('read_file', {}) // Missing required path
            ).rejects.toThrow();
        });
    });

    describe('Tool Registration', () => {
        it('should have all expected tools available', async () => {
            const expectedTools = [
                'read_file',
                'write_file',
                'run_script',
                'ollama_chat',
                'generate_code'
            ];

            for (const toolName of expectedTools) {
                // Test that the tool can be executed without throwing "Unknown tool" error
                try {
                    await toolRunner.executeTool(toolName, {});
                } catch (error: any) {
                    // Should not be "Unknown tool" error
                    expect(error.message).not.toContain('Unknown tool');
                }
            }
        });

        it('should reject unknown tools', async () => {
            await expect(
                toolRunner.executeTool('nonexistent_tool', {})
            ).rejects.toThrow('Unknown tool: nonexistent_tool');
        });
    });

    describe('Integration', () => {
        it('should handle complex workflow', async () => {
            // Write a file and read it back
            const testFile = `${mockConfig.paths.outputs}/workflow_test.txt`;
            const content = 'Workflow test content';

            // Write file
            const writeResult = await toolRunner.executeTool('write_file', {
                path: testFile,
                content: content
            });

            expect(writeResult.path).toBe(testFile);

            // Read file back
            const readResult = await toolRunner.executeTool('read_file', {
                path: testFile
            });

            expect(readResult.content).toBe(content);
        });

        it('should maintain tool execution logs', async () => {
            const testFile = `${mockConfig.paths.outputs}/log_test.txt`;
            await fs.writeFile(testFile, 'test content');

            await toolRunner.executeTool('read_file', { path: testFile });
            await toolRunner.executeTool('write_file', { path: testFile, content: 'new content' });

            // Should have logged both executions (start + complete for each)
            expect(mockLogger.info).toHaveBeenCalledWith(
                'Executing tool',
                expect.objectContaining({ tool: 'read_file' })
            );
            expect(mockLogger.info).toHaveBeenCalledWith(
                'Executing tool',
                expect.objectContaining({ tool: 'write_file' })
            );
        });
    });
});

afterAll(() => {
  mock.restore();
});
