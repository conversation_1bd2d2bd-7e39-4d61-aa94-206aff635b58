# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
bun.lockb

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm



# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Banana Bun specific files
# Database files
*.sqlite
*.sqlite3
*.db

# Data directories (these will be created by setup scripts)
banana-data/
banana-bun-data/
chroma_db/
meilisearch_db/

# Media directories
media/
Media/

# Log files
*.log
logs/

# Temporary processing files
incoming/
processing/
archive/
error/
tasks/
outputs/
dashboard/

# AI model files (these can be large)
*.bin
*.gguf
*.safetensors
models/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Test artifacts
test-results.txt
coverage/

# Build artifacts
dist/
build/

# Backup files
*.bak
*.backup
*.old

# Local configuration overrides
.env.local
config.local.json

# Python cache (for any Python scripts)
__pycache__/
*.py[cod]
*$py.class

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Local development files
dev-test.*
test-temp-*.ts
test-temp-*.js
test-results-*.ts
test-results-*.js

# Banana Bun specific ignores
# User data directories (will vary by platform)
/home/<USER>/banana-data/
/home/<USER>/.local/share/banana-bun/
C:/Users/<USER>/Documents/BananaBun/
/Users/<USER>/Library/Application Support/BananaBun/

# Service data
ollama_data/
chromadb_data/
meilisearch_data/
